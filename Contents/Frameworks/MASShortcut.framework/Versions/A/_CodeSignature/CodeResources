<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/Info.plist</key>
		<data>
		xt1pmtwLaQ3rcabKFlD12+qKgJ4=
		</data>
		<key>Resources/MASShortcut.bundle/Contents/Info.plist</key>
		<data>
		RtqpJJuijqLWM2B7PS0QwTj0NWk=
		</data>
		<key>Resources/MASShortcut.bundle/Contents/Resources/cs.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ZVVRS+JSWnysbJ9NRxv++Q0Eems=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/MASShortcut.bundle/Contents/Resources/de.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Ksea8lSCCRkFMyKpuLL7uphlVNM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/MASShortcut.bundle/Contents/Resources/en.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			M2emNqwcxzt4tdfZa4+urBR84Gk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/MASShortcut.bundle/Contents/Resources/es.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8VhkSbuYbvUN45DFamxHKBcWS1o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/MASShortcut.bundle/Contents/Resources/fr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			hA5xmqIzgPboCJBx9HEm6FD4XRk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/MASShortcut.bundle/Contents/Resources/it.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			XK63By4am+366CaL0fViP8CQC/Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/MASShortcut.bundle/Contents/Resources/ja.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			kwf3zxcnbx8Ii8tMkMTiq8zzDMc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/MASShortcut.bundle/Contents/Resources/ko.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			gakEe+ZsISrkggtm0kob9d4tH1o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/MASShortcut.bundle/Contents/Resources/nl.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			w3NRs/3BxlQxJEQj9lgophQpn1Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/MASShortcut.bundle/Contents/Resources/pl.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Fy1yjqqkqMLlxumpWSIHlpUiaKE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/MASShortcut.bundle/Contents/Resources/ru.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			RrWEjcGGfllpkYqGKtOOBdXRFaI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/MASShortcut.bundle/Contents/Resources/zh-Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ERK5NmfKo6hCX+tp+aQN4T1kXCA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/MASShortcut.bundle/Contents/Resources/zh-Hant.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7r7FjcEm5+f8YB4lndNoVLbhn7g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>files2</key>
	<dict>
		<key>Resources/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			7fOblQQ7z69qMhV4jOyFl/DzoWIk8wT0CIUF0oQTneM=
			</data>
		</dict>
		<key>Resources/MASShortcut.bundle/Contents/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			aEY+azAWArGbw/8ilBdY8dWJFU1YpioT9ZdM3smnU+c=
			</data>
		</dict>
		<key>Resources/MASShortcut.bundle/Contents/Resources/cs.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			GQm+mbT8H1kLoGVmrGvlCyCME/4vDlhWZ7E1VXrnwc8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/MASShortcut.bundle/Contents/Resources/de.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			vHnLa3PSXSHMsY7QujIJNiC+LHLWSJkdt7bvDq+KZ9g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/MASShortcut.bundle/Contents/Resources/en.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			hHpfl66gmL8AVoB+dzARQmG1I3igRtnuIpgnIiT+RqA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/MASShortcut.bundle/Contents/Resources/es.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			7cGfmOaUnZshbFsWHWbwjR9akRAss9jIkLvOZBTFCmA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/MASShortcut.bundle/Contents/Resources/fr.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			+MyevtWm4kfFiOSxG7qZBsnjloPnfclj17+gX+z4d8A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/MASShortcut.bundle/Contents/Resources/it.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			7srqwTWO2OTQOnue+BpAYjSCzslNY1u2XUrxRxi93u0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/MASShortcut.bundle/Contents/Resources/ja.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			qSftO1jADFhy4qQq/fataYRrOrvpGW9Ndds/yx8fSls=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/MASShortcut.bundle/Contents/Resources/ko.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			n9AcMneVtJEnU39VU47AZCdMPjI9r00In+id5URNeLo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/MASShortcut.bundle/Contents/Resources/nl.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			cp2IKkUrVkBzsQC8Qz18zB+d0sCqbLCOk83I218GMFE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/MASShortcut.bundle/Contents/Resources/pl.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			p7tIVFgGsabWiW/44/cNz72KUcUf/mnXWMWILHUmDo4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/MASShortcut.bundle/Contents/Resources/ru.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			7hCutfABLdZ5rTVXw2n0NpdAaz48clomo0GeOuSaSwk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/MASShortcut.bundle/Contents/Resources/zh-Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			zrXB669YbB4A19qnNrtNbR9xZBPGYXl3TiJ4Ip0+mvA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/MASShortcut.bundle/Contents/Resources/zh-Hant.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			I5/vQBdbizone+ogJ3WZZmlV2MnptlkI+ARXxDMuR0s=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
