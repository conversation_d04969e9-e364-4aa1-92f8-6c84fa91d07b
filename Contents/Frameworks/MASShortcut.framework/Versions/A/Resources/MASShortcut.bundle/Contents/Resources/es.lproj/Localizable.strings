/* Cancel action button in recording state */
"Cancel" = "Cancelar";

/* Tooltip for non-empty shortcut button */
"Click to record new shortcut" = "Haga clic para grabar nuevo atajo";

/* Tooltip for hint button near the non-empty shortcut */
"Delete shortcut" = "Borrar atajo";

/* VoiceOver title */
"keyboard shortcut" = "atajo de teklado";

/* Alert button when shortcut is already used */
"OK" = "OK";

/* Empty shortcut button in normal state */
"Record Shortcut" = "Grabar atajo";

/* VoiceOver: Shortcut cleared */
"Shortcut cleared" = "Atajo borrado";

/* VoiceOver: Shortcut set */
"Shortcut set" = "Atajo creado";

/* Shortcut glyph name for SPACE key */
"Space" = "Espacio";

/* Title for alert when shortcut is already used */
"The key combination %@ cannot be used" = "La combinación de claves %@ no se puede utilizada";

/* Message for alert when shortcut is already used by the system */
"This combination cannot be used because it is already used by a system-wide keyboard shortcut.\nIf you really want to use this key combination, most shortcuts can be changed in the Keyboard & Mouse panel in System Preferences." = "Esta combinación no se puede utilizar debido a que ya es en us como atajo del sistema.\nSi realmente desea utilizar esta combinación de teclas, la mayoría de los atajos se puede cambiar en el panel de Teclado y Ratón de Preferencias del Sistema.";

/* Message for alert when shortcut is already used */
"This shortcut cannot be used because it is already used by the menu item ‘%@’." = "Este atajo no se puede utilizar debido a que ya es utilizado por el elemento de menú '%@'.";

/* VoiceOver shortcut help */
"To record a new shortcut, click this button, and then type the new shortcut, or press delete to clear an existing shortcut." = "Para grabar un nuevo atajo, haga clic en este botón, a continuar, escriba el nuevo atajo, o pulse borrar para qutar un atajo existente.";

/* Non-empty shortcut button in recording state */
"Type New Shortcut" = "Escribir atajo";

/* Empty shortcut button in recording state */
"Type Shortcut" = "Escribir atajo";

/* Cancel action button for non-empty shortcut in recording state */
"Use Old Shortcut" = "Usa atajo anterior";