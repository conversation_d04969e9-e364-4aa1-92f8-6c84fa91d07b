/* Cancel action button in recording state */
"Cancel" = "Annulla";

/* Tooltip for non-empty shortcut button */
"Click to record new shortcut" = "Cliccare per registrare una nuova combinazione";

/* Tooltip for hint button near the non-empty shortcut */
"Delete shortcut" = "Cancella scorciatoia";

/* VoiceOver title */
"keyboard shortcut" = "Scorciatoia da tastiera";

/* Alert button when shortcut is already used */
"OK" = "OK";

/* Empty shortcut button in normal state */
"Record Shortcut" = "Registra scorciatoia";

/* VoiceOver: Shortcut cleared */
"Shortcut cleared" = "Scorciatoia rimossa";

/* VoiceOver: Shortcut set */
"Shortcut set" = "Scorciatoia impostata";

/* Shortcut glyph name for SPACE key */
"Space" = "Spazio";

/* Title for alert when shortcut is already used */
"The key combination %@ cannot be used" = "Questa combinazione %@ di tasti non può essere usata";

/* Message for alert when shortcut is already used by the system */
"This combination cannot be used because it is already used by a system-wide keyboard shortcut.\nIf you really want to use this key combination, most shortcuts can be changed in the Keyboard & Mouse panel in System Preferences." = "Questa combinazione di tasti non può essere usata perchè è già usata da una scorciatoia da tastiera a livello di Sistema.\nSe volete davvero usare questa combinazione di tasti, la maggior parte delle scorciatoie possono essere cambiate nel pannello Tastiera e Mouse delle Preferenze di Sistema.";

/* Message for alert when shortcut is already used */
"This shortcut cannot be used because it is already used by the menu item ‘%@’." = "Questa combinazione di tasti non può essere usata perchè è già usata dalla voce di menù ‘%@’.";

/* VoiceOver shortcut help */
"To record a new shortcut, click this button, and then type the new shortcut, or press delete to clear an existing shortcut." = "Per registrare una nuova scorciatoia, cliccare su questo pulsante e poi inserire la muova scorciatoia o premere cancella per resettare una scorciatoia esistente.";

/* Non-empty shortcut button in recording state */
"Type New Shortcut" = "Digita nuova";

/* Empty shortcut button in recording state */
"Type Shortcut" = "Digita scorciatoia";

/* Cancel action button for non-empty shortcut in recording state */
"Use Old Shortcut" = "Usare vecchia";
