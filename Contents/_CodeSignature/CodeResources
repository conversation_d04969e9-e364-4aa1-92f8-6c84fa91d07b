<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/AppCenter.bundle/Contents/Info.plist</key>
		<data>
		AYQoQpbjB3XvsQmyFe0NXa/BMS8=
		</data>
		<key>Resources/AppCenter.bundle/Contents/Resources/PrivacyInfo.xcprivacy</key>
		<data>
		RGatnKWrZPnLawe1gWCHbu0IT/8=
		</data>
		<key>Resources/AppCenterAnalytics.bundle/Contents/Info.plist</key>
		<data>
		wf134XBEHNfJBuI10XL9odQ93EI=
		</data>
		<key>Resources/AppCenterAnalytics.bundle/Contents/Resources/PrivacyInfo.xcprivacy</key>
		<data>
		fA8jpoJLS2JgoGqXOmGrIeycIcg=
		</data>
		<key>Resources/AppCenterCrashes.bundle/Contents/Info.plist</key>
		<data>
		2QMtJD0lf1v/Qooo234SFQS4uKc=
		</data>
		<key>Resources/AppCenterCrashes.bundle/Contents/Resources/PrivacyInfo.xcprivacy</key>
		<data>
		ZupvGMEVxL3kw1eYf28mEK40NMk=
		</data>
		<key>Resources/AppIcon.icns</key>
		<data>
		t3/O8QMwbvmjdo4Urn+fnGONrGQ=
		</data>
		<key>Resources/Assets.car</key>
		<data>
		CVQSD/vhJ8vwMiKJctUl1VLR3Mg=
		</data>
		<key>Resources/Base.lproj/ConfigWindow.nib</key>
		<data>
		lgHsSVW304HMHIg2jA2DBna2Z1E=
		</data>
		<key>Resources/Base.lproj/MainMenu.nib</key>
		<data>
		V+grAgiru/gqVJYuR82bWIXiJ/E=
		</data>
		<key>Resources/Base.lproj/PreferenceAbout.nib</key>
		<data>
		C8Kaenq/0A/SKF7SAc3tTkou8eA=
		</data>
		<key>Resources/Base.lproj/PreferenceAdvance.nib</key>
		<data>
		3isQHuHvQm2IS8a9eui4LSPkodQ=
		</data>
		<key>Resources/Base.lproj/PreferenceDns.nib</key>
		<data>
		HZv5Joeu6AhGM/t3eZ6HqNnNCqs=
		</data>
		<key>Resources/Base.lproj/PreferenceGeneral.nib</key>
		<data>
		KbTVIta3nCU+al6gmq6cWvog25A=
		</data>
		<key>Resources/Base.lproj/PreferencePac.nib</key>
		<data>
		aMjHSXH8+XfTZAbUXbp4uUftL+Q=
		</data>
		<key>Resources/Base.lproj/PreferenceRouting.nib</key>
		<data>
		Y5soqrty4tlGQxwnuu6s57yrOPM=
		</data>
		<key>Resources/Base.lproj/PreferenceSubscription.nib</key>
		<data>
		/XC74IvoCHnL2RjrCdELMCBRCBs=
		</data>
		<key>Resources/Base.lproj/QrcodeWindow.nib</key>
		<data>
		SyaJS6dQnTyMmcNx6DBvK9yeNww=
		</data>
		<key>Resources/Base.lproj/ToastWindow.nib</key>
		<data>
		IOZpEOd5p4ykgXIgXDMjidfJdt8=
		</data>
		<key>Resources/GoogleService-Info.plist</key>
		<data>
		6TxDHlylz0J0VJLs00nIBmVd/SM=
		</data>
		<key>Resources/V2rayUTool</key>
		<data>
		MfsyXB5HaA80bwZzxVF0XSCjEVI=
		</data>
		<key>Resources/build.sh</key>
		<data>
		oH9Cyo5qJY36ncgRdlAGtNxuFzE=
		</data>
		<key>Resources/en.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Zk/27w0bcAiSBFcN9hj+E3DPrEo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/install.sh</key>
		<data>
		8kfZJDht4ecucLkuCxpiCN5H/vQ=
		</data>
		<key>Resources/pac/abp.js</key>
		<data>
		mbwYouL9Om9tJyDk/DyjQA/n2oY=
		</data>
		<key>Resources/pac/gfwlist.txt</key>
		<data>
		SE1gARV2FdIRiwaUPKvCOGMwP4I=
		</data>
		<key>Resources/pac/user-rule.txt</key>
		<data>
		mnMJCYIaA2HTk2804TR53de47TA=
		</data>
		<key>Resources/v2ray-core/geoip-only-cn-private.dat</key>
		<data>
		1kVEqUacv3HzWs2PPaamyNKkylU=
		</data>
		<key>Resources/v2ray-core/geoip.dat</key>
		<data>
		mOUZtlWRRNYQ5/7AhrKOxhOEUMA=
		</data>
		<key>Resources/v2ray-core/geosite.dat</key>
		<data>
		ldELWXtHLL8UIctI1GwsExRI5bI=
		</data>
		<key>Resources/v2ray-core/v2ray</key>
		<data>
		8VZodPYhBef75MJG+1MMrXwalYM=
		</data>
		<key>Resources/v2ray-core/v2ray-arm64</key>
		<data>
		8VZodPYhBef75MJG+1MMrXwalYM=
		</data>
		<key>Resources/zh-Hans.lproj/ConfigWindow.strings</key>
		<dict>
			<key>hash</key>
			<data>
			vMJ0PL02oznqPzvGx4N1ZMsKVJ4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh-Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Zk/27w0bcAiSBFcN9hj+E3DPrEo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh-Hans.lproj/MainMenu.strings</key>
		<dict>
			<key>hash</key>
			<data>
			f/Fyjl87oZZ6Q90k0G1sW/Nnua4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh-Hans.lproj/PreferenceAbout.strings</key>
		<dict>
			<key>hash</key>
			<data>
			nU+XaOwrk5RAolwz7RzH5JdvRnI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh-Hans.lproj/PreferenceAdvance.strings</key>
		<dict>
			<key>hash</key>
			<data>
			AzKZkaopVWvooh1Y7v5v33ByQ3A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh-Hans.lproj/PreferenceDns.strings</key>
		<dict>
			<key>hash</key>
			<data>
			CYXRHdUHowQ1crLumTMbMn5Tuoo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh-Hans.lproj/PreferenceGeneral.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/kW0ANs4/FGEdbcZSIKcRU5Ma84=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh-Hans.lproj/PreferencePac.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Cwbcfze4DpjyHSKPDoJBEcavEzQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh-Hans.lproj/PreferenceRouting.strings</key>
		<dict>
			<key>hash</key>
			<data>
			UtH3VlaVjloYuy+Yo/LkXBETuFE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh-Hans.lproj/PreferenceSubscription.strings</key>
		<dict>
			<key>hash</key>
			<data>
			tOC5B2KUpBUfgsU53oeytuVr9wk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh-Hans.lproj/QrcodeWindow.strings</key>
		<dict>
			<key>hash</key>
			<data>
			CjW27llgzOAn90QlyHoVe3z98eo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>files2</key>
	<dict>
		<key>Frameworks/FBLPromises.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			hmZyRk1uwrw34x0Dyt+elhdl7oI=
			</data>
			<key>requirement</key>
			<string>cdhash H"866672464d6ec2bc37e31d03cadf9e961765ee82" or cdhash H"555e821ff95d50c5bafd55a72642ed1c275a0f26"</string>
		</dict>
		<key>Frameworks/FirebaseCore.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			TlOKBaQVJdDQAsYtPXjEWrjtv2I=
			</data>
			<key>requirement</key>
			<string>cdhash H"4e538a05a41525d0d002c62d3d78c45ab8edbf62" or cdhash H"7dcd1489893947aa16615c3a7b7d4d844a3b92f2"</string>
		</dict>
		<key>Frameworks/FirebaseCoreExtension.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			pbB/BIo7ebc1yfkXkj0L0JpEU7Q=
			</data>
			<key>requirement</key>
			<string>cdhash H"a5b07f048a3b79b735c9f917923d0bd09a4453b4" or cdhash H"8b5b4d82affe2bc73e37fb53b96f270895e8c8e2"</string>
		</dict>
		<key>Frameworks/FirebaseCoreInternal.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			RKPmZpW7Knsx+E0LGKf2lWpWD4U=
			</data>
			<key>requirement</key>
			<string>cdhash H"44a3e66695bb2a7b31f84d0b18a7f6956a560f85" or cdhash H"d7577179b7be3fca1fc6c7726f4ca2340d2fe35c"</string>
		</dict>
		<key>Frameworks/FirebaseCrashlytics.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			LRkOK9+9ojgyy5a6flvIxynFMXI=
			</data>
			<key>requirement</key>
			<string>cdhash H"2d190e2bdfbda23832cb96ba7e5bc8c729c53172" or cdhash H"c51ef0c8b76b4c9dc3d48f348cb838cedd78163b"</string>
		</dict>
		<key>Frameworks/FirebaseInstallations.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			J9/YcfE/YJ+CXZpGUmM1xJ+jkxU=
			</data>
			<key>requirement</key>
			<string>cdhash H"27dfd871f13f609f825d9a46526335c49fa39315" or cdhash H"b2b9ab8e8bf71b367bf73392af5c81d9899125cf"</string>
		</dict>
		<key>Frameworks/FirebaseRemoteConfigInterop.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			yFnRheP0x81zBfEJRpCNAPVIUEw=
			</data>
			<key>requirement</key>
			<string>cdhash H"c859d185e3f4c7cd7305f10946908d00f548504c" or cdhash H"d258cc98acd0f4c970a3a3f01a7d713bd62fc7e9"</string>
		</dict>
		<key>Frameworks/FirebaseSessions.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			pn2SP6xz6Rmw5jj7zlfs7JMsm14=
			</data>
			<key>requirement</key>
			<string>cdhash H"a67d923fac73e919b0e638fbce57ecec932c9b5e" or cdhash H"d0382e925dc9102b4b28aa243e3bd5868ea5521f"</string>
		</dict>
		<key>Frameworks/GoogleDataTransport.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			VY2FH0ARoE1YyHz0/NUD/nYk3oA=
			</data>
			<key>requirement</key>
			<string>cdhash H"558d851f4011a04d58c87cf4fcd503fe7624de80" or cdhash H"3631b97305090c3d754e585fc70251201dd1f3bc"</string>
		</dict>
		<key>Frameworks/GoogleUtilities.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			sBjS8uFpWJ2R5wzeixQmOrbIL8I=
			</data>
			<key>requirement</key>
			<string>cdhash H"b018d2f2e169589d91e70cde8b14263ab6c82fc2" or cdhash H"81e103e9f0cbf6fd893f9ee3453d3d66671d3ad4"</string>
		</dict>
		<key>Frameworks/MASShortcut.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			8m+PYhZYUtKCxZ8/kyic20HzrAs=
			</data>
			<key>requirement</key>
			<string>cdhash H"f26f8f62165852d282c59f3f93289cdb41f3ac0b" or cdhash H"a0c123790d8f453587d4a9c37a082cfd1ae4c9f4"</string>
		</dict>
		<key>Frameworks/Preferences.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			VK/cPmaWqGBtKeYmnyC/46/DoTc=
			</data>
			<key>requirement</key>
			<string>cdhash H"54afdc3e6696a8606d29e6269f20bfe3afc3a137" or cdhash H"bbce3574e4a6b673d04e8b0735ad936d0d54ddc0"</string>
		</dict>
		<key>Frameworks/Promises.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			XVVPLwtUqHgTJ5xJQ/pHE7IvCl0=
			</data>
			<key>requirement</key>
			<string>cdhash H"5d554f2f0b54a87813279c4943fa4713b22f0a5d" or cdhash H"d1e2ad755aba17b5c4e0c9781cea8ae09ecf20f6"</string>
		</dict>
		<key>Frameworks/QRCoder.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			HiKep3X9mscCPd7fSRzkT8CsNsY=
			</data>
			<key>requirement</key>
			<string>cdhash H"1e229ea775fd9ac7023ddedf491ce44fc0ac36c6" or cdhash H"4a7618afb683d93d6ba80e2fa9bef42da33a4874"</string>
		</dict>
		<key>Frameworks/Swifter.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			G0Z2wfcFPwsGwEU76+ZIg2zahPs=
			</data>
			<key>requirement</key>
			<string>cdhash H"1b4676c1f7053f0b06c0453bebe648836cda84fb" or cdhash H"8e27c5ca47dd7de0630fa6228a6441f75d4be677"</string>
		</dict>
		<key>Frameworks/SwiftyJSON.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			2Uix7BZDAGwYi0pd6PqtrFW/1r4=
			</data>
			<key>requirement</key>
			<string>cdhash H"d948b1ec1643006c188b4a5de8faadac55bfd6be" or cdhash H"d89eacd21f131755bd35ba5f76ad3ffa853acd8d"</string>
		</dict>
		<key>Frameworks/Yams.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			mSeHwQ6xVdPusysGva9gj2HMKjE=
			</data>
			<key>requirement</key>
			<string>cdhash H"992787c10eb155d3eeb32b06bdaf608f61cc2a31" or cdhash H"64534292d8de3fac8f935924967d2ed9165f598a"</string>
		</dict>
		<key>Frameworks/libswift_Concurrency.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			Pr3e+QFHQ3D2oyoywLeBrPU4s88=
			</data>
			<key>requirement</key>
			<string>cdhash H"3ebddef901474370f6a32a32c0b781acf538b3cf" or cdhash H"2df8f77ca131cd10508992f453212709099ea525"</string>
		</dict>
		<key>Frameworks/nanopb.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			LYJOvQTv5lVO/8H0+y8jJir07Jw=
			</data>
			<key>requirement</key>
			<string>cdhash H"2d824ebd04efe6554effc1f4fb2f23262af4ec9c" or cdhash H"dcd3d6f760b4ead44f4577451762910604f8ca66"</string>
		</dict>
		<key>Resources/AppCenter.bundle/Contents/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			Iohno5V2mbo/s+m7imZvAJLj6ubC6PBDHDOhqxHlxx4=
			</data>
		</dict>
		<key>Resources/AppCenter.bundle/Contents/Resources/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			dFgfSL94ASqWZdkX5gAD+RTvcK/u7kSgXEHLdo7zg/o=
			</data>
		</dict>
		<key>Resources/AppCenterAnalytics.bundle/Contents/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			OjfkYDRuRRvuyKXyZ4WU1siaFx3573v8gk4LbChYl/s=
			</data>
		</dict>
		<key>Resources/AppCenterAnalytics.bundle/Contents/Resources/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			8WFD/hxz+Np3ZNS2JYaiVJPoxPOZXWhA38gatUV4J8s=
			</data>
		</dict>
		<key>Resources/AppCenterCrashes.bundle/Contents/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			0caaBvyy0N/B7TuMpY5N0wg2IJKidVoXZCVdE+F31a8=
			</data>
		</dict>
		<key>Resources/AppCenterCrashes.bundle/Contents/Resources/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			Mhvjuut9WN6SW2OMmPfASyCpHqEJmIEYUT1JqRqphnM=
			</data>
		</dict>
		<key>Resources/AppIcon.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			HsU87X6d2eHxA1UkM9eR/EyApQWH8+PDn37YJlQTAi4=
			</data>
		</dict>
		<key>Resources/Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			PFSoxE0DX06LYrj4+qLHrLNqhHmJ6bzmc+vDxd9A8R8=
			</data>
		</dict>
		<key>Resources/Base.lproj/ConfigWindow.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			WINhn8gwVbYFlXMUNyfWVYSjN3mNp74ELe8TxKGMCQs=
			</data>
		</dict>
		<key>Resources/Base.lproj/MainMenu.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			yyJgYEqb7iQls3IRO7IwG8AZ/VrmVZlnBIzNTPkqH6k=
			</data>
		</dict>
		<key>Resources/Base.lproj/PreferenceAbout.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			yN3nKS12fW6wndV3Ug7G8kdUV8Uwk9SPDxAOAmE5ue8=
			</data>
		</dict>
		<key>Resources/Base.lproj/PreferenceAdvance.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			XQz4lOJHmHH7H+i1I9NHT67R+SMVNGN7EmBMy0Xc2qA=
			</data>
		</dict>
		<key>Resources/Base.lproj/PreferenceDns.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			qRuDwJC/Qb6h55OdyIYrv/HqdUg36yWPzgQas1oFOhQ=
			</data>
		</dict>
		<key>Resources/Base.lproj/PreferenceGeneral.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			k2dDRcOeqE6qXeg3eTnv5uLheyItiIAApKiyxsXss0Y=
			</data>
		</dict>
		<key>Resources/Base.lproj/PreferencePac.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			32EWfUbIvul2ayV4hLReL2OttUXEYkkUIPuCzhf/+6U=
			</data>
		</dict>
		<key>Resources/Base.lproj/PreferenceRouting.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			8mHLYHLLOLzi0Yt9yXbjFgXL+yJht2aAdoBpAMhFwEI=
			</data>
		</dict>
		<key>Resources/Base.lproj/PreferenceSubscription.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			/zUYilhdgtHin2rxPMvuFvm592ZgR+B8kiCaX9LjhTQ=
			</data>
		</dict>
		<key>Resources/Base.lproj/QrcodeWindow.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			OthN24YcCkME6UBqlwfogYgzZ5Z9CoVxx6hq0O2oPBc=
			</data>
		</dict>
		<key>Resources/Base.lproj/ToastWindow.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			uPYbO+6RjJLtf9U9KpSv2MsgOiHXs247tEI6dMVGboA=
			</data>
		</dict>
		<key>Resources/GoogleService-Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			SjGjEwmMQl7veKFr/xiC20oonnCRTI9lq/U7BN2tQ/I=
			</data>
		</dict>
		<key>Resources/V2rayUTool</key>
		<dict>
			<key>hash2</key>
			<data>
			jsfOj8I1rnF8wW+RLpCD/r+1U0fKlGC0+Dffl5EDZQk=
			</data>
		</dict>
		<key>Resources/build.sh</key>
		<dict>
			<key>hash2</key>
			<data>
			IrKIunn9LkvSZqEbpa6XCgOcnscL2FlSfmzJIR1ep1g=
			</data>
		</dict>
		<key>Resources/en.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			pg4tlzgKLMxJ2AVAgT1abzRxTdADbVAA3CWwytlrvto=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/install.sh</key>
		<dict>
			<key>hash2</key>
			<data>
			brjZnLKlqsAm1ArXURoP+6PV57HVRRy+1X2qV10gzRE=
			</data>
		</dict>
		<key>Resources/pac/abp.js</key>
		<dict>
			<key>hash2</key>
			<data>
			Bf562/Df4qcfuoSF//ch+6bAT7fhzLYxOFlMzjOjosY=
			</data>
		</dict>
		<key>Resources/pac/gfwlist.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			gWFsZpuYOf8PHf2Cov5T7o2KeQPiMDCgo1px7FZHdZg=
			</data>
		</dict>
		<key>Resources/pac/user-rule.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			11rU29wPJ8RjqR8CqXt7tL9n0AtXb95Ezc/r7KsFHrE=
			</data>
		</dict>
		<key>Resources/v2ray-core/geoip-only-cn-private.dat</key>
		<dict>
			<key>hash2</key>
			<data>
			93bGmahv2Nd1wdxNLnvASy2agOEaXVpzyWL9qVXZYWI=
			</data>
		</dict>
		<key>Resources/v2ray-core/geoip.dat</key>
		<dict>
			<key>hash2</key>
			<data>
			OvPcmHo60P42ROd62FO5mNHP3hxiRW3I7OGvVCZN3eU=
			</data>
		</dict>
		<key>Resources/v2ray-core/geosite.dat</key>
		<dict>
			<key>hash2</key>
			<data>
			V13CR9soCldKKSn4vnu0UxIfuUN1CmTxZ6MiVKG6qew=
			</data>
		</dict>
		<key>Resources/v2ray-core/v2ray</key>
		<dict>
			<key>hash2</key>
			<data>
			yiZ4zz6JwY4c+53K54jHwiF47Xm4N5ZWo8UI1338gCY=
			</data>
		</dict>
		<key>Resources/v2ray-core/v2ray-arm64</key>
		<dict>
			<key>hash2</key>
			<data>
			yiZ4zz6JwY4c+53K54jHwiF47Xm4N5ZWo8UI1338gCY=
			</data>
		</dict>
		<key>Resources/zh-Hans.lproj/ConfigWindow.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			rrbpxIR8RCk6vzytbt8DRI2GsJA+VJeCkHXtQDV1Zp0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh-Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			pg4tlzgKLMxJ2AVAgT1abzRxTdADbVAA3CWwytlrvto=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh-Hans.lproj/MainMenu.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			l+Ki6KothSp94cNDGLD662grjDwsv4XJ1bV17vaTPtE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh-Hans.lproj/PreferenceAbout.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			68EI/HMN3yv/Wg6xKNiFUjzL17ZTzLhtmSfNMl17VkQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh-Hans.lproj/PreferenceAdvance.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			rgw9JIKN2B12vIos5BDedmokesIzMrt+yRgQkpFtvt8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh-Hans.lproj/PreferenceDns.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			f33zf/5Or2D3//ZNYOF6aFxKmttbyB2ko2mAxbDCbuY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh-Hans.lproj/PreferenceGeneral.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			kfqTuvdq0WYVi0scyugRjJZ7wAFUcwwc5MFgBq//JHQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh-Hans.lproj/PreferencePac.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			A/Vc1K5TfQby+pXZzgWjxIVOeH5xX6zu5JMGiH7gqfY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh-Hans.lproj/PreferenceRouting.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			EPuPOTIdt/7hnNTMTkHCT/y00NOF69rsXvtJ2VOPDpk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh-Hans.lproj/PreferenceSubscription.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			TGXBk9XT9xLeibUhNuazsA+KV6ylSHbk++X7sXbVcr8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh-Hans.lproj/QrcodeWindow.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			EAJ0UgBD9V5t83kpJKmR2AZgDOnP+yW0Ifmu5GySQgk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
