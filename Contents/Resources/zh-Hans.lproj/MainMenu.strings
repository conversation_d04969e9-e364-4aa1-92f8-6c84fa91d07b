
/* Class = "NSMenuItem"; title = "Share QR Code"; ObjectID = "0vX-fS-8FW"; */
"0vX-fS-8FW.title" = "分享二维码";

/* Class = "NSMenuItem"; title = "Make Lower Case"; ObjectID = "0zv-gA-dTG"; */
"0zv-gA-dTG.title" = "Make Lower Case";

/* Class = "NSMenuItem"; title = "Quit"; ObjectID = "28U-8z-8lS"; */
"28U-8z-8lS.title" = "退出";

/* Class = "NSMenuItem"; title = "Edit"; ObjectID = "2CF-Be-R2r"; */
"2CF-Be-R2r.title" = "Edit";

/* Class = "NSMenuItem"; title = "Redo"; ObjectID = "2dI-3i-ukZ"; */
"2dI-3i-ukZ.title" = "Redo";

/* Class = "NSMenuItem"; title = "Undo"; ObjectID = "2yB-5O-rLI"; */
"2yB-5O-rLI.title" = "Undo";

/* Class = "NSMenuItem"; title = "Capitalize"; ObjectID = "3Hg-b3-phw"; */
"3Hg-b3-phw.title" = "Capitalize";

/* Class = "NSMenuItem"; title = "View v2ray log"; ObjectID = "52m-v5-TzY"; */
"52m-v5-TzY.title" = "查看v2ray日志";

/* Class = "NSMenuItem"; title = "Smart Dashes"; ObjectID = "6aH-vb-Jom"; */
"6aH-vb-Jom.title" = "Smart Dashes";

/* Class = "NSMenuItem"; title = "Preferences..."; ObjectID = "6jp-RJ-ww9"; */
"6jp-RJ-ww9.title" = "偏好设置...";

/* Class = "NSMenuItem"; title = "Speech"; ObjectID = "6ks-XF-0pf"; */
"6ks-XF-0pf.title" = "Speech";

/* Class = "NSMenuItem"; title = "Text Replacement"; ObjectID = "7EO-ax-Qem"; */
"7EO-ax-Qem.title" = "Text Replacement";

/* Class = "NSMenuItem"; title = "Smart Copy/Paste"; ObjectID = "7Fw-Hy-HhI"; */
"7Fw-Hy-HhI.title" = "Smart Copy/Paste";

/* Class = "NSMenuItem"; title = "Pac..."; ObjectID = "8ps-CD-zNp"; */
"8ps-CD-zNp.title" = "Pac设置";

/* Class = "NSMenuItem"; title = "Select All"; ObjectID = "8zL-Er-HLk"; */
"8zL-Er-HLk.title" = "Select All";

/* Class = "NSMenu"; title = "V2rayU"; ObjectID = "9n3-TW-9ur"; */
"9n3-TW-9ur.title" = "V2rayU";

/* Class = "NSMenuItem"; title = "Use Selection for Find"; ObjectID = "9rd-LX-kku"; */
"9rd-LX-kku.title" = "Use Selection for Find";

/* Class = "NSMenuItem"; title = "Ping Speed..."; ObjectID = "A9g-Ks-No1"; */
"A9g-Ks-No1.title" = "Ping速度";

/* Class = "NSMenuItem"; title = "Check Document Now"; ObjectID = "BQD-PL-x7i"; */
"BQD-PL-x7i.title" = "Check Document Now";

/* Class = "NSMenuItem"; title = "Check Grammar With Spelling"; ObjectID = "DLa-pc-doS"; */
"DLa-pc-doS.title" = "Check Grammar With Spelling";

/* Class = "NSMenuItem"; title = "Show Spelling and Grammar"; ObjectID = "EZ5-4J-CJW"; */
"EZ5-4J-CJW.title" = "Show Spelling and Grammar";

/* Class = "NSMenuItem"; title = "Cut"; ObjectID = "Jms-CT-Lld"; */
"Jms-CT-Lld.title" = "Cut";

/* Class = "NSMenuItem"; title = "Subscription..."; ObjectID = "JuC-lf-LpQ"; */
"JuC-lf-LpQ.title" = "订阅设置";

/* Class = "NSMenuItem"; ibShadowedToolTip = "ss:// or vmess://"; ObjectID = "Kct-KD-qPN"; */
"Kct-KD-qPN.ibShadowedToolTip" = "ss:// or vmess://";

/* Class = "NSMenuItem"; title = "Import Server From Pasteboard"; ObjectID = "Kct-KD-qPN"; */
"Kct-KD-qPN.title" = "从粘贴板导入服务器";

/* Class = "NSMenu"; title = "Servers"; ObjectID = "NCx-DY-Hm4"; */
"NCx-DY-Hm4.title" = "服务器列表";

/* Class = "NSMenuItem"; title = "Substitutions"; ObjectID = "Ndr-EN-mCf"; */
"Ndr-EN-mCf.title" = "Substitutions";

/* Class = "NSMenuItem"; title = "Pac Mode"; ObjectID = "NrZ-oB-7yd"; */
"NrZ-oB-7yd.title" = "Pac模式";

/* Class = "NSMenuItem"; title = "V2ray-Core: On"; ObjectID = "P34-uO-Vwf"; */
"P34-uO-Vwf.title" = "启动V2ray-core";

/* Class = "NSMenu"; title = "Transformations"; ObjectID = "Qr9-cc-EbP"; */
"Qr9-cc-EbP.title" = "Transformations";

/* Class = "NSMenu"; title = "Find"; ObjectID = "SDb-YB-42i"; */
"SDb-YB-42i.title" = "Find";

/* Class = "NSMenuItem"; title = "Turn v2ray-core Off"; ObjectID = "SML-EF-rdT"; */
"SML-EF-rdT.title" = "停用v2ray-core";

/* Class = "NSMenuItem"; title = "has new version"; ObjectID = "SdL-DD-qZe"; */
"SdL-DD-qZe.title" = "has new version";

/* Class = "NSMenu"; title = "Substitutions"; ObjectID = "VH3-Bb-D9P"; */
"VH3-Bb-D9P.title" = "Substitutions";

/* Class = "NSMenuItem"; title = "Routing"; ObjectID = "VNg-as-9we"; */
"VNg-as-9we.title" = "路由";

/* Class = "NSMenu"; title = "Edit"; ObjectID = "VmT-t8-f8R"; */
"VmT-t8-f8R.title" = "Edit";

/* Class = "NSMenu"; title = "Spelling"; ObjectID = "WvT-b0-HzE"; */
"WvT-b0-HzE.title" = "Spelling";

/* Class = "NSMenuItem"; title = "Scan QR Code From Screen"; ObjectID = "XHn-5w-qWM"; */
"XHn-5w-qWM.title" = "扫描屏幕上的二维码";

/* Class = "NSMenuItem"; title = "Copy"; ObjectID = "Y2E-1o-SKx"; */
"Y2E-1o-SKx.title" = "Copy";

/* Class = "NSMenuItem"; title = "Spelling and Grammar"; ObjectID = "YJ7-xF-H1b"; */
"YJ7-xF-H1b.title" = "Spelling and Grammar";

/* Class = "NSMenuItem"; title = "Configure..."; ObjectID = "YoX-II-o27"; */
"YoX-II-o27.title" = "服务器设置...";

/* Class = "NSMenuItem"; title = "Start Speaking"; ObjectID = "Z1o-Jx-e0t"; */
"Z1o-Jx-e0t.title" = "Start Speaking";

/* Class = "NSMenuItem"; title = "Smart Links"; ObjectID = "aRc-dd-sOs"; */
"aRc-dd-sOs.title" = "Smart Links";

/* Class = "NSMenuItem"; title = "Check for Updates..."; ObjectID = "adw-ht-RC4"; */
"adw-ht-RC4.title" = "检查更新";

/* Class = "NSMenuItem"; title = "Make Upper Case"; ObjectID = "aeo-mB-Ib5"; */
"aeo-mB-Ib5.title" = "Make Upper Case";

/* Class = "NSMenuItem"; title = "View config.json"; ObjectID = "b3o-tG-rp3"; */
"b3o-tG-rp3.title" = "查看config.json";

/* Class = "NSMenuItem"; title = "Servers"; ObjectID = "cio-ej-HL5"; */
"cio-ej-HL5.title" = "服务器列表";

/* Class = "NSMenu"; title = "Speech"; ObjectID = "eWi-K6-dr9"; */
"eWi-K6-dr9.title" = "Speech";

/* Class = "NSMenuItem"; title = "Stop Speaking"; ObjectID = "ell-l8-8vD"; */
"ell-l8-8vD.title" = "Stop Speaking";

/* Class = "NSMenuItem"; title = "Find Previous"; ObjectID = "emH-b3-5bK"; */
"emH-b3-5bK.title" = "Find Previous";

/* Class = "NSMenuItem"; title = "Delete"; ObjectID = "gSN-CV-cuW"; */
"gSN-CV-cuW.title" = "Delete";

/* Class = "NSMenuItem"; title = "Find Next"; ObjectID = "ge2-bT-EMB"; */
"ge2-bT-EMB.title" = "Find Next";

/* Class = "NSMenuItem"; title = "Find…"; ObjectID = "hGZ-5v-1Gx"; */
"hGZ-5v-1Gx.title" = "Find…";

/* Class = "NSMenuItem"; title = "Manual Mode"; ObjectID = "hTu-27-5OL"; */
"hTu-27-5OL.title" = "手动模式(不改变系统代理)";

/* Class = "NSMenuItem"; title = "Help"; ObjectID = "hWR-vJ-0Au"; */
"hWR-vJ-0Au.title" = "帮助";

/* Class = "NSMenu"; title = "V2rayU"; ObjectID = "hzc-Fw-ODy"; */
"hzc-Fw-ODy.title" = "V2rayU";

/* Class = "NSMenuItem"; title = "Correct Spelling Automatically"; ObjectID = "iKs-PU-Nho"; */
"iKs-PU-Nho.title" = "Correct Spelling Automatically";

/* Class = "NSMenuItem"; title = "Smart Quotes"; ObjectID = "m0x-Yi-Gyl"; */
"m0x-Yi-Gyl.title" = "Smart Quotes";

/* Class = "NSMenuItem"; title = "Data Detectors"; ObjectID = "mbF-XK-RkT"; */
"mbF-XK-RkT.title" = "Data Detectors";

/* Class = "NSMenuItem"; title = "Paste"; ObjectID = "qVc-lh-Zka"; */
"qVc-lh-Zka.title" = "Paste";

/* Class = "NSMenuItem"; title = "Show Substitutions"; ObjectID = "qnn-5c-qTN"; */
"qnn-5c-qTN.title" = "Show Substitutions";

/* Class = "NSMenuItem"; title = "Find and Replace…"; ObjectID = "r36-iu-f2X"; */
"r36-iu-f2X.title" = "Find and Replace…";

/* Class = "NSMenuItem"; title = "Global Mode"; ObjectID = "sZL-Iu-xAt"; */
"sZL-Iu-xAt.title" = "全局模式";

/* Class = "NSMenuItem"; title = "Find"; ObjectID = "ssN-pW-aqd"; */
"ssN-pW-aqd.title" = "Find";

/* Class = "NSMenuItem"; title = "Copy HTTP Proxy Shell Export Line"; ObjectID = "tBG-YP-XJr"; */
"tBG-YP-XJr.title" = "复制终端代理命令";

/* Class = "NSMenuItem"; title = "Transformations"; ObjectID = "uSH-Cb-tWZ"; */
"uSH-Cb-tWZ.title" = "Transformations";

/* Class = "NSMenuItem"; title = "Jump to Selection"; ObjectID = "vkH-fu-RjN"; */
"vkH-fu-RjN.title" = "Jump to Selection";

/* Class = "NSMenuItem"; title = "View pac file"; ObjectID = "vvd-2v-T8I"; */
"vvd-2v-T8I.title" = "查看pac文件";

/* Class = "NSMenuItem"; title = "Paste and Match Style"; ObjectID = "wgi-Ri-oTY"; */
"wgi-Ri-oTY.title" = "Paste and Match Style";

/* Class = "NSMenu"; title = "Routing"; ObjectID = "ykF-uD-Nb1"; */
"ykF-uD-Nb1.title" = "路由";

/* Class = "NSMenuItem"; title = "Check Spelling While Typing"; ObjectID = "zAf-Lw-Sh2"; */
"zAf-Lw-Sh2.title" = "Check Spelling While Typing";
