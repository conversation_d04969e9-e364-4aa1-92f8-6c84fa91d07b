
/* Class = "NSTextFieldCell"; title = "Block:"; ObjectID = "0Cd-bg-07K"; */
"0Cd-bg-07K.title" = "阻止ip或域名:";

/* Class = "NSMenuItem"; title = "IPOnDemand"; ObjectID = "2ST-s0-RrI"; */
"2ST-s0-RrI.title" = "IPOnDemand";

/* Class = "NSTextFieldCell"; title = "Direct:"; ObjectID = "3hZ-qS-PUG"; */
"3hZ-qS-PUG.title" = "直连ip或域名:";

/* Class = "NSButtonCell"; title = "save"; ObjectID = "8g2-Nj-m07"; */
"8g2-Nj-m07.title" = "保存";

/* Class = "NSTextFieldCell"; title = "Rouing Rules"; ObjectID = "E4W-rN-0eq"; */
"E4W-rN-0eq.title" = "路由规则列表";

/* Class = "NSTextFieldCell"; title = "Custom Rule Name"; ObjectID = "Ecy-wl-v5i"; */
"Ecy-wl-v5i.title" = "自定义路由名称";

/* Class = "NSTextFieldCell"; title = "Routing Rule:"; ObjectID = "J2T-Pf-s5D"; */
"J2T-Pf-s5D.title" = "路由规则:";

/* Class = "NSMenuItem"; title = "AsIs"; ObjectID = "Kl2-gr-bsh"; */
"Kl2-gr-bsh.title" = "AsIs";

/* Class = "NSTextFieldCell"; title = "Custom Rule JSON Text"; ObjectID = "Q0v-Ic-TRt"; */
"Q0v-Ic-TRt.title" = "自定义路由 json 内容";

/* Class = "NSTextFieldCell"; title = "Domain Strategy:"; ObjectID = "Qp3-K0-xA9"; */
"Qp3-K0-xA9.title" = "域名策略:";

/* Class = "NSTextFieldCell"; title = "Text Cell"; ObjectID = "YQ1-7i-YOl"; */
"YQ1-7i-YOl.title" = "";

/* Class = "NSTextFieldCell"; title = "Proxy:"; ObjectID = "cfS-8I-8Au"; */
"cfS-8I-8Au.title" = "代理ip或域名:";

/* Class = "NSMenuItem"; title = "IPIfNonMatch"; ObjectID = "dSb-GT-n2n"; */
"dSb-GT-n2n.title" = "IPIfNonMatch";

/* Class = "NSTextFieldCell"; title = "like: { \"domainStrategy\": \"IPOnDemand\", \"rules\": []}"; ObjectID = "ejH-X7-8Fb"; */
"ejH-X7-8Fb.title" = "例如: { \"domainStrategy\": \"IPOnDemand\", \"rules\": []}";

/* Class = "NSTextFieldCell"; title = "* Set the rules line by line: domain or ip"; ObjectID = "ymo-ds-No9"; */
"ymo-ds-No9.title" = "* 按行设置域名或ip";
