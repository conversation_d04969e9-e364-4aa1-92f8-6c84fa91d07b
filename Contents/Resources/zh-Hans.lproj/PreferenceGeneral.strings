
/* Class = "NSTextFieldCell"; title = "Toggle V2ray On/Off:"; ObjectID = "0rn-re-Wn3"; */
"0rn-re-Wn3.title" = "启停 V2ray:";

/* Class = "NSButtonCell"; title = "Automatically update servers from subscriptions "; ObjectID = "1FR-Yu-tqb"; */
"1FR-Yu-tqb.title" = "自动更新订阅列表(启动和唤醒时)";

/* Class = "NSButtonCell"; title = "Check for Updates..."; ObjectID = "2p9-GL-XZ9"; */
"2p9-GL-XZ9.title" = "检查更新";

/* Class = "NSTextFieldCell"; title = "Switch Proxy Mode:"; ObjectID = "Eu4-bo-oYs"; */
"Eu4-bo-oYs.title" = "切换代理模式:";

/* Class = "NSButtonCell"; title = "Launch V2rayU at login"; ObjectID = "NZE-cI-j04"; */
"NZE-cI-j04.title" = "开机自动启动";

/* Class = "NSTextFieldCell"; title = "Related file locations:"; ObjectID = "Oke-cL-DfS"; */
"Oke-cL-DfS.title" = "相关文件路径:";

/* Class = "NSButtonCell"; title = "Feedback..."; ObjectID = "QjX-NH-23u"; */
"QjX-NH-23u.title" = "问题反馈...";

/* Class = "NSButtonCell"; title = "Automatically select fastest server"; ObjectID = "WsS-I0-2Zs"; */
"WsS-I0-2Zs.title" = "自动选择最快的服务器";

/* Class = "NSButtonCell"; title = "Check for updates automutically"; ObjectID = "m2S-Mu-rFM"; */
"m2S-Mu-rFM.title" = "自动检查版本更新";

/* Class = "NSTextFieldCell"; title = "~/.V2rayU/\n~/Library/Preferences/net.yanue.V2rayU.plist"; ObjectID = "vHZ-BS-uFb"; */
"vHZ-BS-uFb.title" = "~/.V2rayU/\n~/Library/Preferences/net.yanue.V2rayU.plist";
